import cloudy from "@assets/cloudy.png";
import moon from "@assets/moon.png";
import sun from "@assets/sun.png";
import { Badge, type BadgeConfig } from "@components/common";

export type OpportunityType = "hot" | "warm" | "cold";

interface OpportunityBadgeConfig extends BadgeConfig {
  type: OpportunityType;
}

const OPPORTUNITY_BADGE_CONFIG: Record<OpportunityType, OpportunityBadgeConfig> = {
  cold: {
    containerClasses: "border-primary bg-success-content",
    icon: cloudy,
    iconAlt: "Cloudy",
    label: "COLD",
    textClasses: "text-primary",
    type: "cold",
  },
  hot: {
    containerClasses: "border-secondary bg-secondary-content/30",
    icon: sun,
    iconAlt: "Sun",
    label: "HOT",
    textClasses: "text-error-content",
    type: "hot",
  },
  warm: {
    containerClasses: "border-warning-content bg-accent/30",
    icon: moon,
    iconAlt: "Moon",
    label: "WARM",
    textClasses: "text-warning-content",
    type: "warm",
  },
};

interface OpportunityBadgeProps {
  type?: OpportunityType;
  className?: string;
}

export const OpportunityBadge = ({ type, className }: OpportunityBadgeProps) => {
  return <Badge type={type} className={className} config={OPPORTUNITY_BADGE_CONFIG} />;
};

export const OPPORTUNITY_OPTIONS = (["hot", "warm", "cold"] as OpportunityType[]).map((type) => ({
  label: <OpportunityBadge type={type} />,
  value: type,
}));
