import {
  CONTACT_CHANNEL_OPTIONS,
  FOLLOW_UP_STATUS_OPTIONS,
  OPPORTUNITY_OPTIONS,
  SERVICE_OPTIONS,
} from "@components/badge";
import { useTranslation } from "react-i18next";
import type { FormField } from "./interface";

export const AddLeadFormField = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const formFields: FormField[] = [
    {
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      id: "opportunity",
      label: t("addLead.opportunity"),
      options: OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
    },
    {
      id: "followUpStatus",
      label: t("addLead.followUpStatus"),
      options: FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("addLead.followUpStatus"),
      type: "select",
    },
    {
      id: "contactChannel",
      label: t("addLead.contactChannel"),
      options: CONTACT_CHANNEL_OPTIONS,
      placeholder: t("addLead.contactChannel"),
      required: true,
      type: "select",
    },
    {
      id: "servicesOfInterest",
      label: t("addLead.servicesOfInterest"),
      options: SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
    },
    {
      defaultValue: today,
      id: "startDate",
      label: t("addLead.startDate"),
      type: "date",
    },
    {
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
    {
      disabled: true,
      id: "followUpDate",
      label: t("addLead.followUpDate"),
      type: "input",
    },
  ];

  return formFields;
};
